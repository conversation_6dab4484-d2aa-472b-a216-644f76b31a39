package tradeup

import (
	"math"
	"tradeup_calc/types"
)

func WearFloatToInt(wear float64) string {
	switch {
	case wear >= 0 && wear < 0.07:
		return "Factory New"
	case wear >= 0.07 && wear < 0.15:
		return "Minimal Wear"
	case wear >= 0.15 && wear < 0.38:
		return "Field-Tested"
	case wear >= 0.38 && wear < 0.45:
		return "Well-Worn"
	case wear >= 0.45 && wear <= 1.0:
		return "Battle-Scarred"
	default:
		return ""
	}
}

func calculateSteamNet(price float64) float64 {
	if price <= 0 {
		return 0
	}

	roundTo := func(val, step float64) float64 {
		// Scale to integer multiples of step
		scaled := val / step
		// Round manually by adding 0.5 for positive, -0.5 for negative
		if scaled >= 0 {
			scaled = float64(int(scaled + 0.5))
		} else {
			scaled = float64(int(scaled - 0.5))
		}
		r := scaled * step
		if r < 0.01 {
			return 0.01
		}
		return r
	}

	V := roundTo(price/11.5, 0.01)
	G := roundTo(price/23, 0.01)

	net := price - V - G - 0.01
	if net < 0 {
		net = 0
	}

	// Round final result to 2 decimals
	return math.Round(net*100) / 100
}

const invStep2 = 100.0 // 1 / 0.01

func roundFloat2(val float64) float64 {
    if val >= 0 {
        return float64(int64(val*invStep2 + 0.5)) / invStep2
    }
    return float64(int64(val*invStep2 - 0.5)) / invStep2
}

const invStep4 = 10000.0 // 1 / 0.0001

func roundFloat4(val float64) float64 {
    if val >= 0 {
        return float64(int64(val*invStep4 + 0.5)) / invStep4
    }
    return float64(int64(val*invStep4 - 0.5)) / invStep4
}

func SimulateTradeup(inputSkins [2]types.InputSkin, outputSkins []types.OutputSkin) (types.TradeupOutput, string) {
	if inputSkins[0].Rarity == "Covert" {
		return types.TradeupOutput{}, "covert skins are not eligible for tradeups"
	}

	// --- Calculate input worth using cache ---
	var inputWorth float64
	for _, v := range inputSkins {
		inputWorth += v.Price
	}
	inputWorth = roundFloat2(inputWorth)

	// --- Average float ---
	sum := 0.0
	for _, v := range inputSkins {
		sum += v.Wear
	}
	avgFloat := roundFloat2(sum/10.0)

	// --- Calculate output skin floats ---
	var minFloat, maxFloat float64
	for i := range outputSkins {
		minFloat = outputSkins[i].WearRange[0]
		maxFloat = outputSkins[i].WearRange[1]

		outputSkins[i].Wear = ( (maxFloat - minFloat) * avgFloat ) + minFloat
		outputSkins[i].Wear = roundFloat4(outputSkins[i].Wear)
	}

	// --- Output skins ---
	inputCollectionCounts := make(map[string]int, 2)
	for _, v := range inputSkins {
		inputCollectionCounts[v.Collection]++
	}

	// --- Compute chances ---
	ballots := 0
	outcomeCollections := make(map[string]int)
	for _, skin := range outputSkins {
		outcomeCollections[skin.Collection]++
	}

	for _, skin := range inputSkins {
		ballots += outcomeCollections[skin.Collection]
	}

	outputWorth := 0.0
	expectedNet := 0.0
	oddsToProfit := 0.0
	oddsToProfitFee := 0.0

	// Compute chance, output worth, net worth, and odds in one pass
	ballotsF := float64(ballots)
	for i := range outputSkins {
		outputSkins[i].Chance = float64(inputCollectionCounts[outputSkins[i].Collection]) / ballotsF
		outputSkins[i].Price = outputSkins[i].Prices[WearFloatToInt(outputSkins[i].Wear)]

		price := outputSkins[i].Price
		if price == 0 {
			return types.TradeupOutput{}, "no price found for skinName"
		}

		outputWorth += price * outputSkins[i].Chance

		netPrice := calculateSteamNet(price)
		expectedNet += netPrice * outputSkins[i].Chance

		if price > inputWorth {
			oddsToProfit += outputSkins[i].Chance
		}
		if netPrice > inputWorth {
			oddsToProfitFee += outputSkins[i].Chance
		}
	}

	// Round metrics
	metrics := types.OutputMetrics{
		OddsToProfit:      roundFloat2(math.Max(0, math.Min(oddsToProfit*100, 100))),
		OddsToProfitFee:   roundFloat2(math.Max(0, math.Min(oddsToProfitFee*100, 100))),
		AverageFloat:      avgFloat,
		InputWorth:        inputWorth,
		OutputWorth:       roundFloat2(outputWorth),
		OutcomeWorthFees:  roundFloat2(expectedNet),
		Profit:            roundFloat2(outputWorth-inputWorth),
		ProfitFees:        roundFloat2(expectedNet-inputWorth),
		Profitability:     roundFloat2((outputWorth/inputWorth)*100),
		ProfitabilityFees: roundFloat2((expectedNet/inputWorth)*100),
	}

	// fmt.Println(metrics.ProfitabilityFees)

	return types.TradeupOutput{
		Skins:   outputSkins,
		Metrics: metrics,
	}, ""
}