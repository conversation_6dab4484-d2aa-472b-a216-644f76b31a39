package main

import (
	"fmt"
	"math"
	"os"
	"runtime"
	"runtime/pprof"
	"sync"
	"time"
	"tradeup_calc/types"

	"github.com/bytedance/sonic"
)

var rarities = [6]string{"Consumer", "Industrial", "Mil-Spec", "Restricted", "Classified", "Covert"}



type TradeupOutput struct {
	InputSkins  []InputSkinReadable  `json:"input_skins"`
	OutputSkins []OutputSkinReadable `json:"output_skins"`
	Metrics     types.OutputMetrics        `json:"outputMetrics"`
}

type InputSkinReadable struct {
	Name       string  `json:"name"`
	Wear       float64 `json:"wear"`
	Rarity     string  `json:"rarity"`
	Quantity   int     `json:"quantity"`
	Collection string  `json:"collection"`
	Price      float64 `json:"price"`
}

type OutputSkinReadable struct {
	Name       string             `json:"name"`
	Wear       float64            `json:"wear"`
	Rarity     string             `json:"rarity"`
	Collection string             `json:"collection"`
	WearRange  [2]float64         `json:"wear_range"`
	Prices     map[string]float64 `json:"prices"`
	Price      float64            `json:"price"`
	Chance     float64            `json:"chance"`
}

// Helper functions for simulation
func roundFloat2(val float64) float64 {
    const invStep2 = 100.0
    if val >= 0 {
        return float64(int64(val*invStep2 + 0.5)) / invStep2
    }
    return float64(int64(val*invStep2 - 0.5)) / invStep2
}

func roundFloat4(val float64) float64 {
    const invStep4 = 10000.0
    if val >= 0 {
        return float64(int64(val*invStep4 + 0.5)) / invStep4
    }
    return float64(int64(val*invStep4 - 0.5)) / invStep4
}

func calculateSteamNet(price float64) float64 {
	// Valve's cut
	vRaw := price / 11.5
	v := math.Floor(vRaw*100) / 100
	if v < 0.01 {
		v = 0.01
	}

	// Game developer cut
	gRaw := price / 23
	g := math.Floor(gRaw*100) / 100
	if g < 0.01 {
		g = 0.01
	}

	// Net amount received
	net := price - v - g
	return roundFloat2(net - 0.01)
}

func wearFloatToString(wear float64) string {
	if wear <= 0.07 {
		return "Factory New"
	} else if wear <= 0.15 {
		return "Minimal Wear"
	} else if wear <= 0.38 {
		return "Field-Tested"
	} else if wear <= 0.45 {
		return "Well-Worn"
	} else {
		return "Battle-Scarred"
	}
}

func simulateTradeup(inputSkins [2]types.InputSkin, outputSkins []types.OutputSkin) types.TradeupOutput {
	// Calculate input worth
	var inputWorth float64
	for _, v := range inputSkins {
		inputWorth += v.Price*
	}
	inputWorth = roundFloat2(inputWorth)

	// Average float
	sum := 0.0
	for _, v := range inputSkins {
		sum += v.Wear
	}
	avgFloat := roundFloat2(sum / 10.0)

	// Calculate output skin floats
	for i := range outputSkins {
		minFloat := outputSkins[i].WearRange[0]
		maxFloat := outputSkins[i].WearRange[1]
		outputSkins[i].Wear = ((maxFloat - minFloat) * avgFloat) + minFloat
		outputSkins[i].Wear = roundFloat4(outputSkins[i].Wear)
	}

	// Count input collections
	inputCollectionCounts := make(map[string]int, 2)
	for _, v := range inputSkins {
		inputCollectionCounts[v.Collection]++
	}

	// Count outcome collections
	ballots := 0
	outcomeCollections := make(map[string]int)
	for _, skin := range outputSkins {
		outcomeCollections[skin.Collection]++
	}

	for _, skin := range inputSkins {
		ballots += outcomeCollections[skin.Collection]
	}

	outputWorth := 0.0
	expectedNet := 0.0
	oddsToProfit := 0.0
	oddsToProfitFee := 0.0

	// Compute chance, output worth, net worth, and odds in one pass
	ballotsF := float64(ballots)
	for i := range outputSkins {
		outputSkins[i].Chance = float64(inputCollectionCounts[outputSkins[i].Collection]) / ballotsF
		wearStr := wearFloatToString(outputSkins[i].Wear)
		outputSkins[i].Price = outputSkins[i].Prices[wearStr]

		price := outputSkins[i].Price
		if price == 0 {
			return types.TradeupOutput{}
		}

		outputWorth += price * outputSkins[i].Chance

		netPrice := calculateSteamNet(price)
		expectedNet += netPrice * outputSkins[i].Chance

		if price > inputWorth {
			oddsToProfit += outputSkins[i].Chance
		}
		if netPrice > inputWorth {
			oddsToProfitFee += outputSkins[i].Chance
		}
	}

	// Round metrics
	metrics := types.OutputMetrics{
		OddsToProfit:      roundFloat2(math.Max(0, math.Min(oddsToProfit*100, 100))),
		OddsToProfitFee:   roundFloat2(math.Max(0, math.Min(oddsToProfitFee*100, 100))),
		AverageFloat:      avgFloat,
		InputWorth:        inputWorth,
		OutputWorth:       roundFloat2(outputWorth),
		OutcomeWorthFees:  roundFloat2(expectedNet),
		Profit:            roundFloat2(outputWorth - inputWorth),
		ProfitFees:        roundFloat2(expectedNet - inputWorth),
		Profitability:     roundFloat2((outputWorth / inputWorth) * 100),
		ProfitabilityFees: roundFloat2((expectedNet / inputWorth) * 100),
	}

	return types.TradeupOutput{
		Skins:   outputSkins,
		Metrics:     metrics,
	}
}

/// EO String interner and interning utils

func handleCollection(collection types.Collection, allCollections types.AllCollections, combinationsToCheck [][2]int, outChannel chan []types.TradeupOutput) {

	tradeupResults := make([]types.TradeupOutput, 0)
	outputSkins := make([]types.OutputSkin, 30)
	tenXChecked := make(map[string]bool)
	var inputSkins [2]types.InputSkin

	for rarityName, rarity := range collection {

		if rarityName == "Covert" {
			continue
		}

		for skinName, skinData := range rarity {
			for wearName, price := range skinData.Prices {

				if price == 0 {
					continue
				}

				var wearFloat float64

				switch wearName {
					case "Factory New":
						wearFloat = 0.06
					case "Minimal Wear":
						wearFloat = 0.11
					case "Field-Tested":
						wearFloat = 0.26
					case "Well-Worn":
						wearFloat = 0.41
					case "Battle-Scarred":
						wearFloat = 0.60
				}

				// Find the next rarity level
				var outcomeRarityName string
				for i, r := range rarities {
					if r == rarityName {
						if i+1 >= len(rarities) { // if covert, skip
							continue
						}
						outcomeRarityName = rarities[i+1]
						break
					}
				}

				isStattrak := len(skinName) > 10 && skinName[:10] == "StatTrak™ "

				for collectionName2, collection2 := range allCollections {
					for _, rarity2 := range collection2 {
						for skinName2, skinData2 := range rarity2 {
							if skinName == skinName2 {
								continue
							}

							isStattrak2 := len(skinName2) > 10 && skinName2[:10] == "StatTrak™ "
							if isStattrak != isStattrak2 {
								continue
							}

							for wearName2, price2 := range skinData2.Prices {
								if price2 == 0 {
									continue
								}

								var wearFloat2 float64

								switch wearName2 {
									case "Factory New":
										wearFloat2 = 0.06
									case "Minimal Wear":
										wearFloat2 = 0.11
									case "Field-Tested":
										wearFloat2 = 0.26
									case "Well-Worn":
										wearFloat2 = 0.41
									case "Battle-Scarred":
										wearFloat2 = 0.60
								}

								// handle all the tradeup logic
								for _, combo := range combinationsToCheck {

									if combo[0] == 10 {
										if tenXChecked[skinName] {
											continue
										} else {
											tenXChecked[skinName] = true
										}
									}

									inputSkins = [2]types.InputSkin{}

									inputSkins[0] = types.InputSkin{
										Name:       skinName,
										Wear:       wearFloat,
										Rarity:     rarityName,
										Quantity:   combo[0],
										Collection: skinData.Collection,
										Price:      price,
										}
									inputSkins[1] = types.InputSkin{
										Name:       skinName2,
										Wear:       wearFloat2,
										Rarity:     rarityName,
										Quantity:   combo[1],
										Collection: collectionName2,
										Price:      price2,
										}
									// for i := range inputSkins {
									// 	inputSkins[i] = types.InputSkin{
									// 		Name:       skinName,
									// 		Wear:       wearFloat,
									// 		Rarity:     rarityName,
									// 		Collection: skinData.Collection,
									// 		Price:      price,
									// 		}
									// }
	
									outputSkins = outputSkins[:0]

									for outputSkinName, outputSkinData := range collection[outcomeRarityName] {

										isOutputStattrak := len(outputSkinName) > 10 && outputSkinName[:10] == "StatTrak™ "
										if isStattrak != isOutputStattrak {
											continue
										}

										outputSkins = append(outputSkins, types.OutputSkin{
											Name:       outputSkinName,
											Wear:       outputSkinData.WearRange[0],
											Rarity:     outcomeRarityName,
											Collection: outputSkinData.Collection,
											WearRange: outputSkinData.WearRange,
											Prices: outputSkinData.Prices,
											Price:      0.0,
											Chance:     0.0,
											})
									}

									tradeupRes := simulateTradeup(inputSkins, outputSkins)

									if tradeupRes.Metrics.ProfitabilityFees > 300 {
										tradeupResults = append(tradeupResults, tradeupRes)
									}
		
								}


							}
						}
					}
				}


			}
		}
	}

	outChannel <- tradeupResults
}

func main() {

	// --- Start CPU profiling ---
	cpuFile, err := os.Create("cpu_profile.prof")
	if err != nil {
		panic(err)
	}
	defer cpuFile.Close()

	if err := pprof.StartCPUProfile(cpuFile); err != nil {
		panic(err)
	}
	defer pprof.StopCPUProfile() // stop profiling at the end
	// ---------------------------

	// calculate the time since the start of main to the very end
	startTime := time.Now()
	defer func() {
		fmt.Println("Total time: ", time.Since(startTime))
	}()

	var combinationsToCheck = [][2]int{{9, 1}, {8, 2}, {7, 3}, {6, 4}, {5, 5}, {10, 0}}

	// read the json file
    data, err := os.ReadFile("today.json")
    if err != nil {
        panic(err)
    }



	// unmarshal the json into the AllCollections struct using sonic (very fast brrr)
    var allCollections types.AllCollections
    if err := sonic.Unmarshal(data, &allCollections); err != nil {
        panic(err)
    }



	jobs := make(chan types.Collection)
	numWorkers := runtime.NumCPU() // or tweak manually

	outChannel := make(chan []types.TradeupOutput, numWorkers)
	var listenerWg sync.WaitGroup

	// open output file
	outFile, err := os.Create("output2.json")
	if err != nil {
		panic(err)
	}
	defer outFile.Close()

	output := make([]types.TradeupOutput, 0)

	listenerWg.Add(1)
	go func() {
		defer listenerWg.Done()

		for res := range outChannel {
			for _, tradeup := range res {
				output = append(output, tradeup)
			}
		}
	}()

	var wg sync.WaitGroup
	for w := 0; w < numWorkers-1; w++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for c := range jobs {
				handleCollection(c, allCollections, combinationsToCheck, outChannel)
			}
		}()
	}

	// feed jobs
	for _, collection := range allCollections {
		jobs <- collection
	}
	close(jobs)

	wg.Wait()
	close(outChannel)
	listenerWg.Wait()

	fmt.Println("calculated, now writing to file...")

	jsonBytes, err := sonic.MarshalIndent(output, "", "  ")
	if err != nil {
		panic(err)
	}

	if err := os.WriteFile("output.json", jsonBytes, 0644); err != nil {
		panic(err)
	}
}