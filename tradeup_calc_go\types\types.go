package types

/// Input types

type InputSkin struct {
	Name   string  `json:"skinName"`   // skin name
	Wear       float64 `json:"wear"`       // wear value
	Rarity     string  `json:"rarity"`     // rarity name
	Quantity   int     `json:"quantity"`
	Collection string  `json:"collection"`
	Price      float64 `json:"price"`
}

type InputSkinID struct {
	Name   int16  `json:"skinName"`   // skin name id
	Wear       float64 `json:"wear"`       // wear value
	Quantity uint8 `json:"quantity"`
	Rarity     uint8  `json:"rarity"`     // rarity name id
	Collection uint8  `json:"collection"` // collection name id
	Price      float64 `json:"price"`
}

type InputSkinList [10]InputSkin
type InputSkinListID [10]InputSkinID

/// Output types

type OutputSkin struct {
	Name   string  `json:"skinName"`   // skin name
	Wear       float64 `json:"wear"`       // wear value
	Rarity     string  `json:"rarity"`     // rarity name
	Collection string  `json:"collection"` // wear name -> price
	WearRange [2]float64 `json:"wearRange"` // wear name -> price
	Prices map[string]float64 `json:"prices"`
	Price      float64 `json:"price"`      // price of skin
	Chance     float64 `json:"chance"`     // chance of skin
}

type OutputSkinID struct {
	Name   int16  `json:"skinName"`   // skin name id
	Wear       float64 `json:"wear"`       // wear value
	Rarity     uint8  `json:"rarity"`     // rarity name id
	Collection uint8  `json:"collection"` // collection name id
	WearRange [2]float64 `json:"wearRange"` // wear name -> price
	Prices map[uint8]float64 `json:"prices"`
	Price      float64 `json:"price"`      // price of skin
	Chance     float64 `json:"chance"`     // chance of skin
}

type OutputMetrics struct {
	OddsToProfit     float64 `json:"oddsToProfit"`     // odds to profit
	OddsToProfitFee  float64 `json:"oddsToProfitFee"`  // odds to profit with fees
	AverageFloat     float64 `json:"averageFloat"`     // average float of outcome skins
	InputWorth       float64 `json:"inputWorth"`       // worth of input skins
	OutputWorth      float64 `json:"outputWorth"`      // worth of output skins
	OutcomeWorthFees float64 `json:"outcomeWorthFees"` // worth of output skins with fees
	Profit           float64 `json:"profit"`           // profit
	ProfitFees       float64 `json:"profitFees"`       // profit with fees
	Profitability    float64 `json:"profitability"`    // profitability
	ProfitabilityFees float64 `json:"profitabilityFees"` // profitability with fees
}

type TradeupOutput struct {
	InputSkins    [2]InputSkin `json:"skins"`    // list of output skins
	OutputSkins    []OutputSkin `json:"skins"`    // list of output skins
	Metrics  OutputMetrics  `json:"metrics"`  // metrics
}

type TradeupOutputID struct {
	Skins    []OutputSkinID `json:"skins"`    // list of output skins
	Metrics  OutputMetrics  `json:"metrics"`  // metrics
}

type Outcome struct {
	InputItems  [10]InputSkin `json:"inputItems"`
	TradeupData TradeupOutput `json:"tradeupOutput"`
}

type OutcomeID struct {
	InputItems  [10]InputSkinID `json:"inputItems"`
	TradeupData TradeupOutputID `json:"tradeupOutput"`
}

/// Intered navigation types

type SkinID struct {
	WearRange [2]float64         `json:"wearRange"` // always 2 floats
	Prices    map[uint8]float64 `json:"prices"`    // wear name -> price
	Collection uint8           `json:"collection"`
}

type RarityID map[int16]SkinID // map of skinName -> Skin

type CollectionID map[uint8]RarityID // map of rarityName -> Rarity

type AllCollectionsID map[uint8]CollectionID // map of collectionName -> Collection

/// Navigation types

type Skin struct {
	WearRange [2]float64         `json:"wearRange"` // always 2 floats
	Prices    map[string]float64 `json:"prices"`    // wear name -> price
	Collection string           `json:"collection"`
}

type Rarity map[string]Skin // map of skinName -> Skin

type Collection map[string]Rarity // map of rarityName -> Rarity

type AllCollections map[string]Collection // map of collectionName -> Collection

// cache

type OutputSkinKey struct {
	Name       string
	WearStr    string
}

type LocalCache struct {
	LocalOutputSkinCache map[OutputSkinKey]OutputSkin
}